//
//  MemberSelectionAvatarTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/2.
//

import SwiftUI

/**
 * 成员选择头像测试视图
 * 用于验证抽奖配置弹窗中的成员头像显示是否正确
 */
struct MemberSelectionAvatarTestView: View {
    
    @StateObject private var dataManager = DataManager.shared
    @State private var showMemberSelectionPopup = false
    @State private var selectedMember: Member?
    @State private var testResults: [String] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("成员选择头像测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 说明文字
                Text("测试抽奖配置弹窗中的成员头像显示")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                
                // 测试按钮
                Button(action: {
                    showMemberSelectionPopup = true
                    addTestResult("🎯 打开成员选择弹窗")
                }) {
                    HStack {
                        Image(systemName: "person.3.fill")
                            .font(.system(size: 16, weight: .medium))
                        Text("打开成员选择弹窗")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            colors: [Color(hex: "#a9d051"), Color(hex: "#7fb83d")],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(12)
                }
                
                // 当前成员信息
                if let member = selectedMember {
                    selectedMemberCard(member: member)
                }
                
                // 头像映射验证
                avatarMappingSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationBarHidden(true)
        }
        .overlay(
            MemberSelectionPopupView(
                isPresented: $showMemberSelectionPopup,
                members: dataManager.members,
                onMemberSelected: { member in
                    selectedMember = member
                    addTestResult("✅ 选择成员: \(member.displayName) (\(member.roleDisplayName))")
                    addTestResult("🖼️ 头像资源: \(member.avatarImageName)")
                }
            )
        )
    }
    
    // MARK: - 子视图组件
    
    /**
     * 选中成员卡片
     */
    private func selectedMemberCard(member: Member) -> some View {
        HStack(spacing: 16) {
            // 头像显示 - 验证avatarImageName是否正确
            Image(member.avatarImageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 50, height: 50)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(DesignSystem.Colors.primary, lineWidth: 2)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(member.displayName)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(member.roleDisplayName)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("头像资源: \(member.avatarImageName)")
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
        )
    }
    
    /**
     * 头像映射验证区域
     */
    private var avatarMappingSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("头像资源映射验证")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                avatarMappingRow(role: "father", expected: "爸爸头像")
                avatarMappingRow(role: "mother", expected: "妈妈头像")
                avatarMappingRow(role: "son", expected: "男生头像")
                avatarMappingRow(role: "daughter", expected: "女生头像")
                avatarMappingRow(role: "other", expected: "其他头像")
            }
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    /**
     * 头像映射行
     */
    private func avatarMappingRow(role: String, expected: String) -> some View {
        HStack(spacing: 12) {
            // 显示头像
            Image(expected)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 30, height: 30)
                .clipShape(Circle())
            
            Text("\(role) → \(expected)")
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
        }
    }
    
    /**
     * 测试结果区域
     */
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试结果")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(testResults.indices, id: \.self) { index in
                        Text(testResults[index])
                            .font(.system(size: 12, family: .monospaced))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding(12)
            }
            .frame(maxHeight: 150)
            .background(Color(hex: "#f8f9fa"))
            .cornerRadius(8)
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - Helper Methods
    
    /**
     * 添加测试结果
     */
    private func addTestResult(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testResults.append("[\(timestamp)] \(message)")
    }
}

#Preview {
    MemberSelectionAvatarTestView()
}
